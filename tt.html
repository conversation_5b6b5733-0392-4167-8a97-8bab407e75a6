<script>
// 封装文件URL获取函数
async function getFileUrl(fileName,token) {
    try {
        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${token}`);
        
        const requestOptions = {
            method: "POST",  // 使用POST方法
            headers: myHeaders,
            body: fileName,  // 直接使用文件名作为请求体
            redirect: "follow"
        };
        
        const response = await fetch("http://jqtest.free.idcfengye.com/deepseek/log/getPath", requestOptions);
        const result = await response.text();
        const data = JSON.parse(result);
        
        if (data && data.msg) {
            return data.msg;
        } else {
            throw new Error('未找到文件URL');
        }
    } catch (error) {
        console.error('获取文件URL失败:', error);
        throw error;
    }
}
getFileUrl();

</script>