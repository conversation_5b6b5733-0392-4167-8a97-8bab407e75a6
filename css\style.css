
    /* 主题变量 */
    :root {
        --bg-primary: #ffffff;
        --bg-secondary: #f3f4f6;
        --text-primary: #111827;
        --text-secondary: #4b5563;
        --border-color: #e5e7eb;
        --hover-bg: #f9fafb;
        --message-bg-user: #ebf5ff;
        --message-bg-assistant: #f3f4f6;
        --button-primary-bg: #2563eb;
        --button-primary-hover: #1d4ed8;
        --button-primary-text: #ffffff;
    }

    :root[data-theme="dark"] {
        --bg-primary: #111827;
        --bg-secondary: #1f2937;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #374151;
        --hover-bg: #374151;
        --message-bg-user: #1e3a8a;
        --message-bg-assistant: #1f2937;
        --button-primary-bg: #3b82f6;
        --button-primary-hover: #2563eb;
        --button-primary-text: #ffffff;
    }

    /* 全局过渡效果 */
    *, *::before, *::after {
        transition-property: background-color, border-color, color, fill, stroke;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 0.15s;
    }

    .no-transition {
        transition: none !important;
    }

    body {
        background-color: var(--bg-primary);
        color: var(--text-primary);
        transition: background-color 0.3s ease, color 0.3s ease;
    }

    /* Markdown 样式 */
    .markdown-body pre {
        background-color: var(--bg-secondary);
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;
        border: 1px solid var(--border-color);
        max-width: 100%;
        white-space: pre-wrap;
        word-wrap: break-word;
        position: relative;
        /* 日间模式下的代码块背景 */
        background-color: #ffffff;
    }

    .markdown-body pre code {
        font-family: monaco, Consolas, "Lucida Console", monospace;
        font-size: 0.9em;
        background-color: transparent;
        padding: 0;
        border-radius: 0;
        /* 日间模式下的代码文本颜色 */
        color: #24292e;
    }

    .markdown-body p {
        margin: 0.5rem 0;
        line-height: 1.6;
        max-width: 100%;
    }

    .markdown-body ul, .markdown-body ol {
        margin: 0.5rem 0;
        padding-left: 1.5rem;
    }

    .markdown-body li {
        margin: 0.25rem 0;
    }

    .markdown-body blockquote {
        border-left: 4px solid #4a5568;
        padding-left: 1rem;
        margin: 1rem 0;
        color: #718096;
    }

    .dark .markdown-body blockquote {
        border-left-color: #718096;
        color: #a0aec0;
    }

    /* 思考容器样式 */
    .think-container {
        margin: 1rem 0;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .thinking-time {
        font-family: monospace;
        padding: 2px 6px;
        border-radius: 4px;
        background-color: var(--hover-bg);
    }


    .think-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background-color: var(--bg-secondary);
        border-bottom: 1px solid var(--border-color);
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s ease;
    }
    
    .think-header:hover {
        background-color: var(--hover-bg);
    }
    
    .think-header-icon {
        transform: rotate(0deg);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .think-content.collapsed + .think-header .think-header-icon {
        transform: rotate(-90deg);
    }

    .think-content {
        padding: 1rem;
        background-color: var(--bg-primary);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
    }

    .think-content.collapsed {
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
        border-top: 0;
        border-bottom: 0;
        opacity: 0;
        transform: translateY(-10px);
    }

    /* 光标动画 */
    .cursor {
        display: inline-block;
        width: 2px;
        height: 1em;
        background: currentColor;
        margin-left: 2px;
        animation: blink 1s infinite;
    }

    @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0; }
    }

    /* 消息操作按钮 */
    .message-actions {
        opacity: 0;
        transition: opacity 0.2s;
    }

    .message-container:hover .message-actions {
        opacity: 1;
    }

    @media (max-width: 768px) {
        .message-actions {
            opacity: 1;
        }
        .markdown-body pre {
            max-width: 100%;
            font-size: 0.85em;
        }
    }

    /* 对话列表项样式 */
    .conversation-item {
        transition: all 0.2s ease-in-out;
        position: relative;
    }

    .conversation-item:hover {
        background-color: var(--bg-secondary);
    }

    .conversation-item.active {
        background-color: var(--message-bg-user);
        border-left: 4px solid var(--button-primary-bg);
    }

    /* 侧边栏响应式 */
    .sidebar {
        transition: transform 0.3s ease-in-out;
    }

    @media (max-width: 768px) {
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            z-index: 50;
            transform: translateX(-100%);
        }
        .sidebar.active {
            transform: translateX(0);
        }
        #sidebar-overlay.active {
            display: block;
        }
    }

    /* 主题切换按钮 */
    #theme-toggle {
        position: relative;
        overflow: hidden;
    }

    #theme-toggle::after {
        content: '';
        position: absolute;
        inset: 0;
        background-color: currentColor;
        opacity: 0;
        transition: opacity 0.2s;
    }

    #theme-toggle:hover::after {
        opacity: 0.1;
    }

    /* 主题切换动画 */
    .theme-transition {
        animation: theme-fade 0.3s ease;
    }

    @keyframes theme-fade {
        from { opacity: 0.8; }
        to { opacity: 1; }
    }

    /* Toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 1rem;
        right: 1rem;
        background-color: var(--bg-secondary);
        color: var(--text-primary);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        animation: toast-fade 0.3s ease-in-out;
    }

    @keyframes toast-fade {
        from {
            opacity: 0;
            transform: translateY(1rem);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 确保所有颜色相关的样式使用 CSS 变量 */
    .markdown-body {
        color: var(--text-primary);
    }

    /* 回到最新按钮样式 */
    #scroll-to-bottom-btn {
        position: fixed;
        bottom: 80px;
        right: 20px;
        padding: 8px 16px;
        background-color: var(--button-primary-bg);
        color: var(--button-primary-text);
        border-radius: 20px;
        cursor: pointer;
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.3s, transform 0.3s;
        z-index: 40;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    #scroll-to-bottom-btn.visible {
        opacity: 1;
        transform: translateY(0);
    }
    
    #scroll-to-bottom-btn:hover {
        background-color: var(--button-primary-hover);
    }

    /* 移动设备安全区域适配 */
    @supports(padding-bottom: env(safe-area-inset-bottom)) {
        .pb-safe {
            padding-bottom: env(safe-area-inset-bottom, 0);
        }
    }
    
    /* 移动设备输入区域适配 */
    @media (max-width: 768px) {
        #chat-container {
            padding-bottom: 0.25rem;
        }
        
        .pb-safe {
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom, 0.5rem));
        }
    }

    /* PC端输入区域适配 */
    @media (min-width: 769px) {
        .pb-safe {
            padding-bottom: 0;
        }
    }

    /* 模型选择按钮样式 */
    .model-btn {
        border-color: var(--border-color);
        color: var(--text-secondary);
        background-color: var(--bg-primary);
    }
    
    .model-btn:hover {
        background-color: var(--hover-bg);
    }
    
    .model-btn.active {
        background-color: var(--button-primary-bg);
        border-color: var(--button-primary-bg);
        color: var(--button-primary-text);
    }
    
    @media (max-width: 768px) {
        .model-btn {
            padding: 0.375rem 0.75rem;
        }
    }

    /* 分割线样式 */
    .markdown-body hr {
        height: 2px;
        background-color: var(--border-color);
        border: none;
        margin: 2rem 0;
    }

    /* 复制成功提示样式 */
    .copy-toast {
        position: fixed;
        bottom: 16px;
        right: 16px;
        background-color: var(--bg-primary);
        color: var(--text-primary);
        padding: 8px 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 8px;
        z-index: 100;
        animation: toast-slide-up 0.3s ease, toast-fade-out 0.3s ease 1.7s forwards;
    }
    
    @keyframes toast-slide-up {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    @keyframes toast-fade-out {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }

    /* 确保所有内容都不会超出容器 */
    .markdown-body {
        overflow-wrap: break-word;
        word-wrap: break-word;
        word-break: break-word;
        hyphens: auto;
    }
    
    /* 处理长链接 */
    .markdown-body a {
        word-break: break-all;
    }
    
    /* 处理表格 */
    .markdown-body table {
        display: block;
        width: 100%;
        overflow-x: auto;
        max-width: 100%;
        border-spacing: 0;
        border-collapse: collapse;
    }

    .think-container p {
        color: gray;
        font-size: 0.8rem;
    }
    
    .markdown-body table th,
    .markdown-body table td {
        padding: 0.75rem 1.5rem;
        border: 1px solid var(--border-color);
    }
    
    .markdown-body table tr {
        background-color: var(--bg-primary);
        border-top: 1px solid var(--border-color);
    }
    
    .markdown-body table tr:nth-child(2n) {
        background-color: var(--bg-secondary);
    }
    
    .markdown-body table th {
        font-weight: 600;
        background-color: var(--bg-secondary);
    }
    
    /* 处理图片 */
    .markdown-body img {
        max-width: 100%;
        height: auto;
    }
    
    /* 处理行内代码 */
    .markdown-body code {
        word-break: break-all;
        white-space: pre-wrap;
    }

    /* 代码块复制按钮 */
    .code-copy-btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.25rem 0.5rem;
        color: var(--text-secondary);
        background-color: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: 0.25rem;
        opacity: 1;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        transition: all 0.2s ease;
    }

    .code-copy-btn:hover {
        opacity: 1;
        background-color: var(--hover-bg);
    }

    /* 移动端样式优化 */
    @media (max-width: 768px) {
        .code-copy-btn {
            opacity: 1;
            padding: 0.25rem 0.5rem;
        }
    }

    .code-copy-btn.copied {
        color: var(--button-primary-bg);
    }

    /* 深色模式覆盖 */
    :root[data-theme="dark"] .markdown-body pre {
        background-color: var(--bg-secondary);
    }

    :root[data-theme="dark"] .markdown-body pre code,
    :root[data-theme="dark"] .markdown-body code {
        color: var(--text-primary);
    }

    :root[data-theme="dark"] .markdown-body code {
        background-color: var(--bg-secondary);
    }

    /* Markdown 样式 */
    .markdown-body h1,
    .markdown-body h2,
    .markdown-body h3 {
        margin-top: 1.25rem;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    /* 标题样式 */
    .markdown-body h1 {
        font-size: 1.5em;
    }
    
    .markdown-body h2 {
        font-size: 1.3em;
    }
    
    .markdown-body h3 {
        font-size: 1.15em;
    }
    
    .markdown-body h4 {
        font-size: 1.05em;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .markdown-body h5 {
        font-size: 1em;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .markdown-body h6 {
        font-size: 0.95em;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    /* 自定义滚动条样式 */
    #conversation-list::-webkit-scrollbar {
        width: 6px;
    }
    
    #conversation-list::-webkit-scrollbar-track {
        background: transparent;
    }
    
    #conversation-list::-webkit-scrollbar-thumb {
        background-color: var(--border-color);
        border-radius: 3px;
    }
    
    #conversation-list::-webkit-scrollbar-thumb:hover {
        background-color: var(--text-secondary);
    }
    
    /* Firefox 滚动条样式 */
    #conversation-list {
        scrollbar-width: thin;
        scrollbar-color: var(--border-color) transparent;
    }

    /* 输入区域样式 */
    .input-area {
        border-top: 1px solid var(--border-color);
        padding: 1rem;
    }

    @media (max-width: 768px) {
        .input-area {
            padding: 0.5rem; /* 减小手机端的内边距 */
        }
        
        .input-area textarea {
            height: 5rem; /* 减小手机端的输入框高度 */
        }
    }

    /* 修改链接样式 */
    .markdown-body a {
        color: #3b82f6; /* 使用更鲜明的蓝色 */
        text-decoration: none;
        position: relative;
        transition: all 0.2s ease;
        font-weight: 500; /* 稍微加粗 */
        padding: 0 2px; /* 添加一点内边距 */
    }

    .markdown-body a:hover {
        color: #2563eb; /* 悬停时的颜色 */
        background-color: rgba(59, 130, 246, 0.1); /* 添加淡蓝色背景 */
        border-radius: 2px;
    }

    /* 深色模式下的链接颜色调整 */
    :root[data-theme="dark"] .markdown-body a {
        color: #60a5fa; /* 深色模式下使用更亮的蓝色 */
        opacity: 1;
    }

    :root[data-theme="dark"] .markdown-body a:hover {
        color: #93c5fd;
        background-color: rgba(96, 165, 250, 0.2);
    }

    /* 为外部链接添加图标 */
    .markdown-body a[href^="http"]::before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 4px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%233b82f6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        vertical-align: middle;
    }

    :root[data-theme="dark"] .markdown-body a[href^="http"]::before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%2360a5fa'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'/%3E%3C/svg%3E");
    }

    /* 加载动画样式 */
    @keyframes dot-bounce {
        0%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-4px); }
    }

    .loading-dots {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 8px 0;
    }

    .loading-dots .dot {
        width: 6px;
        height: 6px;
        background-color: var(--text-secondary);
        border-radius: 50%;
        animation: dot-bounce 1.4s infinite ease-in-out both;
    }

    .loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
    .loading-dots .dot:nth-child(3) { animation-delay: 0s; }

    /* 修复输入框右边框问题 */
    #user-input {
        outline: none !important;
        box-shadow: none !important;
    }
    
    #user-input:focus {
        border-color: var(--button-primary-bg);
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        outline: none;
    }
    
    /* 确保输入框在所有状态下都有一致的边框 */
    #user-input:focus-visible {
        outline: none;
    }
    
    /* 移除任何可能的浏览器默认样式 */
    textarea {
        appearance: none;
        -webkit-appearance: none;
    }
    
    /* 确保在所有浏览器中一致的行为 */
    textarea:focus {
        outline: none;
    }
    
    /* 修复可能的 Chrome 特定问题 */
    @supports (-webkit-appearance: none) {
        #user-input:focus {
            outline: none !important;
        }
    }
    
    /* 修复可能的 Firefox 特定问题 */
    @-moz-document url-prefix() {
        #user-input:focus {
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
            border-color: var(--button-primary-bg);
        }
    }

    /* 文件预览关闭按钮样式 */
    .file-preview {
        position: relative;
    }
    
    .file-close-btn {
        transition: all 0.2s ease;
    }
    
    .file-close-btn:hover {
        transform: scale(1.1);
    }
/* 删除多余的结束标签 */
