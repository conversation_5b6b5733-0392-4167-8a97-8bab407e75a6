{"name": "slio-chat", "version": "1.0.0", "description": "SlioChat - AI Chat Application", "main": "index.js", "scripts": {"build": "(cp index.html index.html.bak || copy index.html index.html.bak) && node scripts/build.js && webpack --mode production && (mv index.html.bak index.html || move index.html.bak index.html) && (rm -f dist/main.js || del dist\\main.js)", "build:webpack": "webpack --mode production"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/plugin-transform-runtime": "^7.23.7", "@babel/preset-env": "^7.23.7", "@babel/runtime": "^7.23.7", "axios": "^1.7.9", "babel-core": "^7.0.0-bridge.0", "babel-loader": "^9.1.3", "cheerio": "^1.0.0", "core-js": "^3.35.0", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.6.0", "node-fetch": "^2.7.0", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}}