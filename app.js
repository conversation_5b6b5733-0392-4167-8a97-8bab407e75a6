// 全局变量和状态管理
let currentConversation = null;
let conversations = [];
let isGenerating = false;
let currentTheme = 'light';
let apiConfig = {
    endpoint: '',
    apiKey: '',
    model: ''
};

// DOM元素
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-button');
const chatContainer = document.getElementById('chat-container');
const conversationList = document.getElementById('conversation-list');
const themeToggle = document.getElementById('theme-toggle');
const scrollToBottomBtn = document.getElementById('scroll-to-bottom-btn');
const sidebar = document.querySelector('.sidebar');
const sidebarOverlay = document.getElementById('sidebar-overlay');
const toggleSidebarBtn = document.getElementById('toggle-sidebar-btn');

// 主题切换
function toggleTheme() {
    const root = document.documentElement;
    currentTheme = root.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
    root.setAttribute('data-theme', currentTheme);
    localStorage.setItem('theme', currentTheme);
    showThemeToast();
}

// 显示主题切换提示
function showThemeToast() {
    const toast = document.createElement('div');
    toast.className = 'theme-toast';
    toast.textContent = `已切换到${currentTheme === 'dark' ? '深色' : '浅色'}主题`;
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 2000);
}

// 初始化主题
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    currentTheme = savedTheme;
}

// 滚动到底部按钮显示控制
function toggleScrollToBottomButton() {
    const container = chatContainer;
    const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;
    scrollToBottomBtn.classList.toggle('visible', !isNearBottom);
}

// 滚动到底部
function scrollToBottom() {
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// 移动端侧边栏控制
function toggleSidebar() {
    sidebar.classList.toggle('active');
    sidebarOverlay.classList.toggle('active');
}

// 代码块复制功能
function setupCodeCopy() {
    document.querySelectorAll('.markdown-body pre').forEach(pre => {
        if (!pre.querySelector('.code-copy-btn')) {
            const button = document.createElement('button');
            button.className = 'code-copy-btn';
            button.innerHTML = '<svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/></svg>复制';
            button.addEventListener('click', () => {
                const code = pre.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    button.classList.add('copied');
                    button.innerHTML = '<svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M5 13l4 4L19 7"/></svg>已复制';
                    setTimeout(() => {
                        button.classList.remove('copied');
                        button.innerHTML = '<svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/></svg>复制';
                    }, 2000);
                });
            });
            pre.appendChild(button);
        }
    });
}

// 思考容器折叠/展开功能
function setupThinkContainers() {
    document.querySelectorAll('.think-header').forEach(header => {
        header.addEventListener('click', () => {
            const content = header.parentElement.querySelector('.think-content');
            content.classList.toggle('collapsed');
        });
    });
}

// 发送消息函数
async function sendMessage() {
    if (isGenerating || !userInput.value.trim()) return;

    const message = userInput.value.trim();
    userInput.value = '';
    userInput.style.height = 'auto';

    // 创建用户消息元素
    const userMessageDiv = document.createElement('div');
    userMessageDiv.className = 'message user-message';
    userMessageDiv.innerHTML = `<div class="message-content">${message}</div>`;
    chatContainer.appendChild(userMessageDiv);

    // 创建AI回复元素
    const aiMessageDiv = document.createElement('div');
    aiMessageDiv.className = 'message ai-message';
    aiMessageDiv.innerHTML = `<div class="message-content">思考中...</div>`;
    chatContainer.appendChild(aiMessageDiv);

    scrollToBottom();
    isGenerating = true;

    try {
        // 这里添加与AI服务器的通信逻辑
        // 示例：
        const response = await fetch(apiConfig.endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiConfig.apiKey}`
            },
            body: JSON.stringify({
                model: apiConfig.model,
                messages: [{ role: 'user', content: message }]
            })
        });

        if (!response.ok) {
            throw new Error('API请求失败');
        }

        const data = await response.json();
        aiMessageDiv.innerHTML = `<div class="message-content">${data.choices[0].message.content}</div>`;
    } catch (error) {
        aiMessageDiv.innerHTML = `<div class="message-content error">发生错误：${error.message}</div>`;
    } finally {
        isGenerating = false;
        scrollToBottom();
    }
}

// 事件监听器设置
function setupEventListeners() {
    // 主题切换
    themeToggle.addEventListener('click', toggleTheme);

    // 滚动监听
    chatContainer.addEventListener('scroll', toggleScrollToBottomButton);
    scrollToBottomBtn.addEventListener('click', scrollToBottom);

    // 移动端侧边栏
    toggleSidebarBtn.addEventListener('click', toggleSidebar);
    sidebarOverlay.addEventListener('click', toggleSidebar);

    // 输入框自动调整高度
    userInput.addEventListener('input', () => {
        userInput.style.height = 'auto';
        userInput.style.height = userInput.scrollHeight + 'px';
    });

    // 发送消息
    sendButton.addEventListener('click', sendMessage);
    userInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
}

// 初始化应用
function initApp() {
    initTheme();
    setupEventListeners();
    setupCodeCopy();
    setupThinkContainers();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initApp);