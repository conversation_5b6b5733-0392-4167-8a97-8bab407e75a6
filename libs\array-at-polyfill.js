/**
 * Array.prototype.at() polyfill
 * https://github.com/tc39/proposal-relative-indexing-method
 */
(function() {
  if (!Array.prototype.at) {
    Object.defineProperty(Array.prototype, 'at', {
      value: function(n) {
        // ToInteger() abstract op
        n = Math.trunc(n) || 0;
        // Allow negative indexing from the end
        if (n < 0) n += this.length;
        // OOB access is guaranteed to return undefined
        if (n < 0 || n >= this.length) return undefined;
        // Otherwise, this is just normal property access
        return this[n];
      },
      writable: true,
      enumerable: false,
      configurable: true
    });
  }
})();