
const toolUrl = 'http://jqtest.free.idcfengye.com';
//token解析
async function deepseekLogin(token) {
    try {
        // 直接返回固定字符串，模拟成功登录
        return "9661794";
        const response = await fetch(`${toolUrl}/deepseekLogin`, {
            method: 'POST',
            headers: {
                'Content-Type': 'text/plain',
                'Authorization': `Bearer ${token}`
            },
            body: token
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.text();
        return result;
    } catch (error) {
        console.error('Deepseek 登录失败:', error);
        throw error;
    }
}

// 封装文件URL获取函数
async function getFileUrl(fileName,token) {
    try {
        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${token}`);
        
        const requestOptions = {
            method: "POST",  // 使用POST方法
            headers: myHeaders,
            body: fileName,  // 直接使用文件名作为请求体
            redirect: "follow"
        };
        
        const response = await fetch(`${toolUrl}/deepseek/log/getPath`, requestOptions);
        const result = await response.text();
        const data = JSON.parse(result);
        
        if (data && data.msg) {
            return data.msg;
        } else {
            throw new Error('未找到文件URL');
        }
    } catch (error) {
        console.error('获取文件URL失败:', error);
        throw error;
    }
}

// 封装文件下载函数
async function downloadFile(fileName, token) {
    try {
        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${token}`);
        
        const requestOptions = {
            method: "POST",
            headers: myHeaders,
            body: fileName,
            redirect: "follow"
        };  
        const response = await fetch(`${toolUrl}/deepseek/log/downloadFile`, requestOptions);
        
        if (!response.ok) {
            throw new Error(`下载失败! 状态码: ${response.status}`);
        }
        
        // 获取文件blob
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none'; // 隐藏元素
        a.href = url;
        a.download = fileName;
        a.target = '_self'; // 强制在当前页面处理
        

        a.click();
        
        
         // 清理
         setTimeout(() => {
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }, 100);
        
        return true;
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}