class MessageHandler {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    async sendMessage(message, options = {}) {
        const {
            conversationId = '',
            user = '',
            files = []
        } = options;

        const requestBody = {
            query: message,
            user: user,
            conversation_id: conversationId,
            files,
            inputs: {},
            response_mode: 'streaming',
            auto_generate_name:false
        };

        try {
            const response = await fetch(`${this.baseUrl}/chat-messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response;
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        }
    }

    async handleStreamResponse(response, callbacks = {}) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let thinking = '';
        let answer = '';
        let conversationId = '';
        let message_id = '';
        let task_id = '';
        let isThinking = false;
        let thinkingBuffer = '';
        let answerBuffer = '';
        let combinedData = ''; // 用于存储组合的数据流
        let lastThinkingContent = ''; // 添加这行来跟踪上一次的思考内容
        let referencedFiles = []; // 存储引用的文件信息
        let isFirstThinking = true; // 添加标记，用于跟踪是否是第一次思考内容

        try {
            let isFirstChunk = true; // 添加一个标志来标记是否是第一个数据块
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = JSON.parse(line.slice(6));
                        if (data.conversation_id) {
                            conversationId = data.conversation_id;
                            callbacks.onConversationId?.(conversationId);
                        }
                        if (data.message_id) {
                            message_id = data.message_id;
                            callbacks.onMessageId?.(message_id);
                        }
                        if (data.task_id) {
                            task_id = data.task_id;
                            callbacks.onTaskId?.(task_id);
                        }
                        if (data.event === 'message') {
                            console.log('数据:', data);
                            
                           // 将当前数据添加到组合数据中
                             combinedData += data.answer;
                            
                            // 检查是否开始思考
                            if (combinedData.includes('<think>') && !isThinking) {
                                isThinking = true;
                                thinkingBuffer = '';
                                // 提取<think>之后的内容
                                const thinkStart = combinedData.indexOf('<think>') + '<think>'.length;
                                thinkingBuffer = combinedData.substring(thinkStart);
                            }
                            
                            // 处理思考过程
                            if (isThinking) {
                                // 检查是否结束思考
                                if (combinedData.includes('</think>')) {
                                    isThinking = false;
                                    
                                    // 提取思考内容（<think>和</think>之间的内容）
                                    const thinkEndIndex = combinedData.indexOf('</think>');
                                    const thinkStartIndex = combinedData.indexOf('<think>') + '<think>'.length;
                                    thinking = combinedData.substring(thinkStartIndex, thinkEndIndex);
                                    
                                    // 提取答案部分（</think>之后的内容）
                                    const answerPart = combinedData.substring(thinkEndIndex + '</think>'.length);
                                    if (answerPart) {
                                        answerBuffer = answerPart;
                                        answer = answerBuffer;
                                        callbacks.onAnswer?.(answer);
                                    }
                                } else {
                             // 更新思考内容
                                let cleanedThinking = data.answer;
                                
                                // 如果当前数据块包含<think>标签，从标签后开始取
                                const thinkStart = cleanedThinking.indexOf('<think>');
                                if (thinkStart !== -1) {
                                    cleanedThinking = cleanedThinking.substring(thinkStart + '<think>'.length);
                                }
                                
    
                                // 移除所有可能的</think相关内容（包括部分匹配）
                                    let thinkEndIndex = -1;
                                    // 只检查完整的</think>标签
                                    const endTag = '</think>';
                                    thinkEndIndex = cleanedThinking.indexOf(endTag);
                                    
                                    if (thinkEndIndex !== -1) {
                                        cleanedThinking = cleanedThinking.substring(0, thinkEndIndex);
                                    }
                                    
                                    // 确保没有任何不完整的结束标签
                                    if (!cleanedThinking.includes('</think') && cleanedThinking) {
                                        if (isFirstThinking) {
                                            thinkingBuffer = cleanedThinking;
                                            isFirstThinking = false;
                                        } else {
                                            thinkingBuffer += cleanedThinking;
                                        }
                                        callbacks.onThinking?.(thinkingBuffer);
                                    }
                                }
                            } else {
                                // 如果不在思考模式，且数据不包含<think>标签
                                if (!data.answer.includes('<think>')) {
                                    answerBuffer += data.answer;
                                    answer = answerBuffer;
                                    callbacks.onAnswer?.(answer);
                                }
                            }
                        } else if (data.event === 'message_end') {
                            console.log('流式输出结束');
                            
                            // 处理引用文件信息
                            if (data.metadata && data.metadata.retriever_resources) {
                                // 提取文件信息并去重
                                const fileMap = new Map();
                                
                                data.metadata.retriever_resources.forEach(resource => {
                                    if (resource.document_id && resource.document_name) {
                                        // 使用文件ID作为键，确保去重
                                        if (!fileMap.has(resource.document_id)) {
                                            fileMap.set(resource.document_id, {
                                                id: resource.document_id,
                                                name: resource.document_name,
                                                dataset_id: resource.dataset_id,
                                                dataset_name: resource.dataset_name
                                            });
                                        }
                                    }
                                });
                                
                                // 转换Map为数组
                                referencedFiles = Array.from(fileMap.values());
                                console.log('引用文件信息:', referencedFiles);
                                // 调用回调函数，传递引用文件信息
                                if (callbacks.onReferencedFiles) {
                                    callbacks.onReferencedFiles(referencedFiles);
                                }
                            }
                        }
                    }
                }
            }
            console.log("答案:"+answer,"思考过程:"+thinking,conversationId);
            return {
                thinking,
                answer,
                conversationId,
                referencedFiles // 添加引用文件信息到返回结果
            };
        } catch (error) {
            callbacks.onError?.(error);
            throw error;
        }
    }

    async renameConversation(conversationId, options = {}) {
        const {
            name = '',
            auto_generate = true,
            user = ''
        } = options;

        try {
            const response = await fetch(`${this.baseUrl}/conversations/${conversationId}/name`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    name,
                    auto_generate,
                    user
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('重命名会话失败:', error);
            throw error;
        }
    }

    async getConversations(options = {}) {
        const {
            user = '',
            lastId = '',
            limit = 20
        } = options;

        try {
            const queryParams = new URLSearchParams({
                user: user,
                last_id: lastId,
                limit: limit
            });

            const response =  await fetch(`${this.baseUrl}/conversations?${queryParams}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return  await response.json();
        } catch (error) {
            console.error('获取会话列表失败:', error);
            throw error;
        }
    }

    async getMessages(options = {}) {
        const {
            user = '',
            conversationId = '',
            limit = 20,
            first_id = '',
        } = options;

        try {
            const queryParams = new URLSearchParams({
                user: user,
                conversation_id: conversationId,
                limit: limit,
                first_id: first_id,
            });

            const response = await fetch(`${this.baseUrl}/messages?${queryParams}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取消息历史失败:', error);
            throw error;
        }
    }
    
    async deleteConversation(conversationId, user) {
        try {
            const response = await fetch(`${this.baseUrl}/conversations/${conversationId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    user: user
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response;
        } catch (error) {
            console.error('删除对话失败:', error);
            throw error;
        }
    }

     /**
     * 上传文件到服务器
     * @param {File} file - 要上传的文件对象
     * @param {Object} options - 上传选项
     * @param {string} options.user - 用户标识
     * @returns {Promise<Object>} - 返回上传结果，包含文件ID等信息
     */
     async uploadFile(file, options = {}) {
        const {
            user = ''
        } = options;

        try {
            // 创建FormData对象
            const formData = new FormData();
            formData.append('file', file);
            formData.append('user', user);

            // 发送请求
            const response = await fetch(`${this.baseUrl}/files/upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`文件上传失败! 状态码: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('文件上传失败:', error);
            throw error;
        }
    }

        /**
     * 批量上传多个文件
     * @param {Array<File>} files - 文件对象数组
     * @param {Object} options - 上传选项
     * @param {string} options.user - 用户标识
     * @returns {Promise<Array<Object>>} - 返回所有文件的上传结果
     */
        async uploadFiles(files, options = {}) {
            const {
                user = ''
            } = options;
            
            try {
                // 使用Promise.all并行上传所有文件
                const uploadPromises = Array.from(files).map(file => 
                    this.uploadFile(file, { user })
                );
                
                return await Promise.all(uploadPromises);
            } catch (error) {
                console.error('批量上传文件失败:', error);
                throw error;
            }
        }
}
    