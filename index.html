<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>AiChat - Beta测试</title>
    <!-- 本地依赖文件 -->
    <link rel="stylesheet" href="./css/style.css">
    <link rel="stylesheet" href="./libs/github-dark.min.css">
    <script src="./libs/tailwindcss.min.js"></script>
    <script src="./libs/array-at-polyfill.js"></script>
    <script src="./libs/marked.min.js"></script>
    <script src="./libs/highlight.min.js"></script>
    <script src="./libs/jschardet.min.js"></script>
    <script src="./libs/pdf.min.js"></script>
    <script src="./libs/mammoth.browser.min.js"></script>
    <script src="./libs/xlsx.full.min.js"></script>
    <script src="./libs/message-handler.js"></script>
    <script>
        // 设置 PDF.js worker 路径
        pdfjsLib.GlobalWorkerOptions.workerSrc = './libs/pdf.worker.min.js';
    </script>
    <!-- 自定义样式 -->
    <script src="./libs/tool.js"></script>
    <script>
        // 配置marked选项
        marked.use({
            gfm: true,
            breaks: true,
            pedantic: false,
            sanitize: false,
            smartLists: true,
            smartypants: false,
            mangle: false,
            headerIds: false,
            xhtml: true,
            highlight: function(code, lang) {
                try {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(code, { language: lang }).value;
                    }
                    return hljs.highlightAuto(code).value;
                } catch (e) {
                    console.warn('Highlight.js error:', e);
                    return code;
                }
            }
        });

        // 注册Excel语言支持
        hljs.registerLanguage('excel', function(hljs) {
            return {
                name: 'Excel',
                aliases: ['xlsx', 'xls'],
                case_insensitive: true,
                keywords: {
                    built_in: 'SUM AVERAGE COUNT IF SUMIF COUNTIF VLOOKUP HLOOKUP INDEX MATCH ' +
                              'CONCATENATE CONCAT TEXT LEN TRIM LEFT RIGHT MID FIND SEARCH ' +
                              'DATE DATEVALUE TODAY NOW YEAR MONTH DAY DATEDIF ' +
                              'AND OR NOT TRUE FALSE ' +
                              'ABS ROUND ROUNDUP ROUNDDOWN INT RAND RANDBETWEEN ' +
                              'MAX MIN LARGE SMALL'
                },
                contains: [
                    {
                        className: 'operator',
                        begin: /[-+*/=<>:&%^]/
                    },
                    {
                        className: 'number',
                        begin: /\b\d+(\.\d+)?\b/
                    },
                    {
                        className: 'string',
                        begin: /"/,
                        end: /"/,
                        contains: [{ begin: /""/, relevance: 0 }]
                    },
                    {
                        className: 'function',
                        begin: /\b[A-Z]+\(/,
                        end: /\)/,
                        relevance: 10,
                        contains: [
                            {
                                className: 'params',
                                begin: /\(/,
                                end: /\)/,
                                contains: [
                                    {
                                        className: 'number',
                                        begin: /\b\d+(\.\d+)?\b/
                                    },
                                    {
                                        className: 'string',
                                        begin: /"/,
                                        end: /"/,
                                        contains: [{ begin: /""/, relevance: 0 }]
                                    },
                                    {
                                        className: 'cell-reference',
                                        begin: /[A-Z]+\d+/
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        className: 'cell-reference',
                        begin: /[A-Z]+\d+/
                    }
                ]
            };
        });
    </script>
</head>
<body class="bg-[var(--bg-primary)] text-[var(--text-primary)] transition-colors duration-200">
<div class="flex h-screen">
    <!-- 侧边栏 -->
    <div id="sidebar" class="sidebar w-72 md:w-64 bg-[var(--bg-secondary)] border-r border-[var(--border-color)] flex flex-col z-30">
        <div class="p-4 border-b border-[var(--border-color)]">
            <button id="new-chat-btn" onclick="createNewConversation()"
                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                新建对话
            </button>
        </div>
        <div class="flex-1 overflow-y-auto" id="conversation-list">
            <!-- 对话列表将在这里动态生成 -->
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col">
        <!-- 顶部标题栏 -->
        <div class="p-4 md:p-4 p-1 border-b border-[var(--border-color)] flex justify-between items-center">
            <div class="flex-1 min-w-0 flex items-center space-x-2">
                <!-- 移动端菜单按钮 -->
                <button id="menu-toggle" class="md:hidden p-1 rounded-lg hover:bg-[var(--hover-bg)] z-20">
                    <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
                <!-- 对话标题 -->
                <div class="flex-1 min-w-0 flex items-center">
                    <h2 id="current-conversation-title" 
                        class="text-lg md:text-lg font-medium cursor-pointer truncate max-w-[200px] md:max-w-[320px] lg:max-w-[500px] hover:text-[var(--button-primary-bg)]"
                        onclick="startEditingTitle(this)"></h2>
                    <input type="text" 
                            id="title-input"
                            class="hidden w-full max-w-[200px] md:max-w-[320px] lg:max-w-[500px] px-2 py-1 border border-[var(--border-color)] rounded bg-[var(--bg-primary)] text-[var(--text-primary)]"
                            onblur="saveTitle()"
                            onkeypress="handleTitleKeyPress(event)">
                </div>
            </div>
            <div class="flex items-center space-x-1 flex-shrink-0">
                <!-- 主题切换按钮 -->
                <button id="theme-toggle" class="p-1 md:p-2 rounded-lg hover:bg-[var(--hover-bg)] transition-colors">
                    <!-- 暗色模式图标 -->
                    <svg id="dark-icon" class="w-5 h-5 md:w-6 md:h-6 text-[var(--text-primary)]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                    </svg>
                    <!-- 亮色模式图标 -->
                    <svg id="light-icon" class="w-5 h-5 md:w-6 md:h-6 text-[var(--text-primary)]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                    </svg>
                </button>
 
            </div>
        </div>

        <!-- 聊天内容区 -->
        <div id="chat-container" class="flex-1 overflow-y-auto p-4 md:p-4 p-2 pb-safe">
            <div id="messages" class="max-w-3xl mx-auto space-y-4"></div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
            <div class="max-w-3xl mx-auto flex flex-col space-y-4">
                <div class="relative">
                    <!-- 添加文件上传按钮 -->
                    <div class="absolute top-1 left-2">
                        <input type="file" id="file-upload" accept=".txt,.pdf,.docx,.doc,.xlsx,.xls" class="hidden" onchange="handleFileUpload(this)" multiple>
                        <button onclick="document.getElementById('file-upload').click()"
                                class="p-2.5 text-[var(--text-secondary)] hover:bg-[var(--hover-bg)] rounded-md transition-colors duration-200"
                                title="上传一个或多个文件">
                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                            </svg>
                        </button>
                    </div>
                    <textarea id="user-input" rows="3"
                              class="resize-none w-full px-12 py-2 pr-36 border border-[var(--border-color)] rounded-md bg-[var(--bg-primary)] text-[var(--text-primary)] focus:ring-2 focus:ring-[var(--button-primary-bg)]"
                              placeholder="输入消息... (Ctrl+Enter 发送)" ></textarea>
                    <div class="absolute top-1 right-2">
                        <div class="flex items-center justify-between">
                            <select id="model-select" 
                                    onchange="switchModel(this.value)"
                                    class="pl-2 pr-2 p-2.5 text-sm rounded-md bg-[var(--bg-primary)] text-[var(--text-primary)] focus:ring-2 focus:ring-[var(--button-primary-bg)] cursor-pointer hover:bg-[var(--hover-bg)] transition-colors">
                            </select>
                        </div>
                    </div>
                    <div class="absolute bottom-2 right-2 flex items-center space-x-1.5">
                            <button onclick="togglePause()"
                                    class="hidden p-2.5 text-[var(--text-secondary)] hover:bg-[var(--hover-bg)] rounded-md transition-colors duration-200"
                                    id="pause-button">
                                <svg id="pause-icon" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <svg id="play-icon" class="hidden w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                            <button onclick="stopGeneration()"
                                    class="hidden p-2.5 text-[var(--text-secondary)] hover:bg-[var(--hover-bg)] rounded-md transition-colors duration-200"
                                    id="stop-button">
                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <button onclick="sendMessage()"
                                    class="p-2.5 text-[var(--button-primary-bg)] hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors duration-200"
                                    id="send-button">
                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                        d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 添加遮罩层 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden md:hidden" onclick="toggleSidebar()"></div>

<button id="scroll-to-bottom-btn" class="hidden" onclick="scrollToBottom(true)">
    <div class="flex items-center space-x-2">
        <span>查看新消息</span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
    </div>
</button>

<!-- 自定义对话框 -->
<div id="custom-dialog" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-[var(--bg-primary)] rounded-lg shadow-xl max-w-sm w-[calc(100%-2rem)] md:w-full mx-4 transform transition-all">
        <div class="p-4">
            <h3 id="dialog-title" class="text-lg font-medium text-[var(--text-primary)] mb-2"></h3>
            <p id="dialog-message" class="text-[var(--text-secondary)] leading-relaxed"></p>
        </div>
        <div class="px-4 py-3 bg-[var(--bg-secondary)] rounded-b-lg flex justify-end space-x-2 md:space-x-3">
            <button id="dialog-cancel" 
                    class="flex-1 md:flex-none px-4 py-2 text-[var(--text-secondary)] hover:bg-[var(--hover-bg)] rounded-md transition-colors">
                取消
            </button>
            <button id="dialog-confirm" 
                    class="flex-1 md:flex-none px-4 py-2 bg-[var(--button-primary-bg)] text-white rounded-md hover:bg-[var(--button-primary-hover)] transition-colors">
                确定
            </button>
        </div>
    </div>
</div>

<!-- 自定义提示框 -->
<div id="custom-alert" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-[var(--bg-primary)] rounded-lg shadow-xl max-w-sm w-[calc(100%-2rem)] md:w-full mx-4 transform transition-all">
        <div class="p-4">
            <p id="alert-message" class="text-[var(--text-primary)] leading-relaxed"></p>
        </div>
        <div class="px-4 py-3 bg-[var(--bg-secondary)] rounded-b-lg flex justify-end">
            <button id="alert-confirm" 
                    class="flex-1 md:flex-none min-w-[120px] px-4 py-2 bg-[var(--button-primary-bg)] text-white rounded-md hover:bg-[var(--button-primary-hover)] transition-colors">
                确定
            </button>
        </div>
    </div>
</div>



<!-- 添加隐藏的文件输入元素 -->
<input type="file" id="chat-import" accept=".json" style="display: none;" onchange="handleFileImport(this)">
<script>
    // 通用的复制文本函数
    async function copyTextToClipboard(text) {
        try {
            // 优先使用现代的 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            }
            
            // 回退方案：使用传统的 execCommand 方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                textArea.remove();
                return true;
            } catch (err) {
                textArea.remove();
                return false;
            }
        } catch (err) {
            console.error('复制失败:', err);
            return false;
        }
    }

    // 显示复制成功/失败的提示
    function showCopyToast(success = true) {
        const toast = document.createElement('div');
        toast.className = 'copy-toast';
        toast.innerHTML = success ? `
            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>复制成功</span>
        ` : `
            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span>复制失败</span>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
    }

    // API配置
    const API_CONFIG = {
        defaultUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions', //用来生成标题的 key
        defaultKey: '4343afe401f046afa592b4fa4f33cdab.zRzWA4Thv2FYZ2ba',  // 用来生成标题的 key
        defaultModel: 'deepseek-r1-distill-llama-70b', // 用来生成标题的模型
        defaultSystemPrompt: '你是一位专业、友善且富有同理心的AI助手。你会根据问题的复杂程度调整回答方式：对于复杂问题，你会条理清晰地展示思考过程并给出详细解释；对于简单问题，你会直接给出准确简洁的答案。你善于倾听用户的需求，用平易近人的语气进行交流，在必要时会主动询问以更好地理解用户意图。你的回答始终保持客观专业，并在适当时候提供有见地的建议。',
        models: {
            //deepseek蒸馏模型
            // 'deepseek-r1-distill-qwen-32b': {
            //     name: 'r1-fast',
            //     type: 'thinking',
            //     url: 'https://0f68edf33a3a4219a5ab9d9ae6b3034c-cn-hangzhou.alicloudapi.com/compatible-mode/v1/chat/completions ',
            //     key: 'none',
            // },
             //deepseek外网版本
            //  'deepseek-reasoner': {
            //     name: 'deepseek外网',
            //     type: 'thinking',
            //     url: 'https://api.deepseek.com/chat/completions',
            //     key: 'sk-41f5cd43526a45e3ab8af52292747e0a',
            // },
            'deepseek-r1-distill-llama-70b': {
                name: 'deepseek内网',
                type: 'thinking', 
                url: 'http://jqdify.3s.tunnelfrp.com/v1',
                key: 'app-WC5IV06elI1LUUpYJZT1gtVW',
            }
            //可以新增更多的模型
        },
        contextCount: 20,  // 设置上下文消息数量
        chatTypes: {
            normal: {
                name: '普通对话',
                systemPrompt: '你是一位专业、友善且富有同理心的AI助手。你会根据问题的复杂程度调整回答方式：对于复杂问题，你会条理清晰地展示思考过程并给出详细解释；对于简单问题，你会直接给出准确简洁的答案。你善于倾听用户的需求，用平易近人的语气进行交流，在必要时会主动询问以更好地理解用户意图。你的回答始终保持客观专业，并在适当时候提供有见地的建议。'
            }
        },
        search: {
            url: 'https://api.bochaai.com/v1/web-search',
            enabled: false,  // 控制是否启用搜索功能
            token: 'xxxxxxxxx'  // bochaai的api key
        }
    };

    // Markdown 配置
    marked.setOptions({
        breaks: true,
        gfm: true,
        highlight: function(code, language) {
            if (language && hljs.getLanguage(language)) {
                try {
                    return hljs.highlight(code, { language }).value;
                } catch (err) {}
            }
            return code;
        },
        langPrefix: 'hljs language-'
    });

    function processMathJax(element) {
        //TODO
        return;
    }

    // 状态变量
    let currentEditingMessage = null;
    let currentConversationId = null;
    let conversations = {};
    let isPaused = false;
    let currentReader = null;
    let currentRequestController = null;
    let isGenerating = false;
    let autoScroll = true;
    let currentUser = null;
    // 在文件顶部添加全局变量声明
    let messageHandler = null;
    let has_more = false ;
    let last_record_id = '';
    //  在全局变量区域添加
    let pendingFiles = []; // 存储待发送的文件信息

    // 添加文件类型映射
    const FILE_TYPE_MAPPING = {
        // 文档类型
        'TXT': 'document', 'MD': 'document', 'MARKDOWN': 'document', 
        'PDF': 'document', 'HTML': 'document', 'XLSX': 'document', 
        'XLS': 'document', 'DOCX': 'document', 'CSV': 'document',
        'EML': 'document', 'MSG': 'document', 'PPTX': 'document',
        'PPT': 'document', 'XML': 'document', 'EPUB': 'document',
        
        // 图片类型
        'JPG': 'image', 'JPEG': 'image', 'PNG': 'image',
        'GIF': 'image', 'WEBP': 'image', 'SVG': 'image',
        
        // 音频类型
        'MP3': 'audio', 'M4A': 'audio', 'WAV': 'audio',
        'WEBM': 'audio', 'AMR': 'audio',
        
        // 视频类型
        'MP4': 'video', 'MOV': 'video', 'MPEG': 'video',
        'MPGA': 'video'
    };


    // 主题管理
    const themeToggle = document.getElementById('theme-toggle');
    const html = document.documentElement;
    const thinkingHtml = `
                                <div class="think-container">
                                    <div class="think-header" onclick="toggleThinking(this)">
                                        <svg class="think-header-icon w-4 h-4 text-[var(--text-secondary)]" viewBox="0 0 20 20" fill="currentColor" style="transform: rotate(0deg);">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                        <span class="text-[var(--text-secondary)]">🤔 思考过程</span>
                                    </div>
                                    <div class="think-content" style="display: block;">
                                        <div class="markdown-body text-[var(--text-primary)]"></div>
                                    </div>
                                </div>
                                <div class="response-content markdown-body text-[var(--text-primary)] mt-4"></div>`;


     // 添加初始化函数
    function initMessageHandler() {
        const modelId = document.getElementById('model-select').value;
        const modelConfig = API_CONFIG.models[modelId];
        messageHandler = new MessageHandler(
            modelConfig.url,
            modelConfig.key || API_CONFIG.defaultKey
        );
    }                                
    function initializeTheme() {
        html.classList.remove('no-transition');
        
        const userPreference = localStorage.getItem('theme');
        const systemPreference = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (userPreference === 'dark' || (!userPreference && systemPreference)) {
            html.classList.add('dark');
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            html.classList.remove('dark');
            document.documentElement.setAttribute('data-theme', 'light');
        }
        updateThemeIcons();
    }

    function toggleTheme() {
        html.classList.add('no-transition');

        const isDark = html.classList.contains('dark');
        if (isDark) {
            html.classList.remove('dark');
            document.documentElement.setAttribute('data-theme', 'light');
            localStorage.setItem('theme', 'light');
        } else {
            html.classList.add('dark');
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
        }

        void html.offsetHeight;
        
        html.classList.remove('no-transition');

        showThemeToast(!isDark);
        updateThemeIcons();
    }

    function showThemeToast(isDark) {
        const existingToast = document.querySelector('.theme-toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = `已切换至${isDark ? '暗色' : '亮色'}主题`;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, 2000);
    }

    function updateThemeIcons() {
        const darkIcon = document.getElementById('dark-icon');
        const lightIcon = document.getElementById('light-icon');
        const isDark = html.classList.contains('dark');
        
        darkIcon.style.display = isDark ? 'block' : 'none';
        lightIcon.style.display = isDark ? 'none' : 'block';
    }

    // 移动端菜单控制
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
    }

    // 初始化应用
     function initialize() {
        initializeTheme();   
        // 初始化模型选择下拉框
        const modelSelect = document.getElementById('model-select');
        Object.entries(API_CONFIG.models).forEach(([modelId, modelInfo]) => {
            const option = document.createElement('option');
            option.value = modelId;
            option.id = `model-${modelId}`;
            option.textContent = modelInfo.name;
            modelSelect.appendChild(option);
        });
        
        // 刷新对话列表和消息
        refreshConversationList().then(() => {
            console.log('conversations 对象：', conversations);
        // 如果没有对话，创建一个新对话
        if (Object.keys(conversations).length === 0) {
            const conversationId = 'conv_' + Date.now();
            conversations[conversationId] = {
                title: '普通对话 1',
                messages: [],
                systemPrompt: API_CONFIG.chatTypes.normal.systemPrompt,
                type: 'normal'
            };
            saveConversations();
            currentConversationId = conversationId;
        } else {
            // 优先使用上一次选中的对话
            const lastSelectedId = localStorage.getItem('lastSelectedConversation');
            if (lastSelectedId && conversations[lastSelectedId]) {
                currentConversationId = lastSelectedId;
            } else {
                // 如果没有上次选中的对话或该对话已被删除，使用最新的对话
                currentConversationId = Object.keys(conversations)[0];
            }
        }

        
        // 先刷新消息，然后等待渲染完成后滚动
        // refreshMessages();
        scrollWhenReady();

        // 更新当前对话标题
        document.getElementById('current-conversation-title').textContent = 
            conversations[currentConversationId].title;

        // 设置事件监听器
        document.getElementById('menu-toggle').addEventListener('click', toggleSidebar);
        themeToggle.addEventListener('click', toggleTheme);

        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                if (e.matches) {
                    html.classList.add('dark');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    html.classList.remove('dark');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
                updateThemeIcons();
            }
        });

        // 监听输入框回车事件
        document.getElementById('user-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 修改滚动监听器
        const chatContainer = document.getElementById('chat-container');
        chatContainer.addEventListener('scroll', function() {
            const scrollButton = document.getElementById('scroll-to-bottom-btn');
            const isNearBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 100;
            scrollButton.classList.toggle('visible', !isNearBottom);
            // 当用户向上滚动时，禁用自动滚动
            if (!isNearBottom) {
                autoScroll = false;
            }
            // 当用户滚动到底部时，重新启用自动滚动
            if (isNearBottom) {
                autoScroll = true;
            }
        });

        // 恢复上次选择的模型
        const savedModel = localStorage.getItem('preferred-model');
        if (savedModel && API_CONFIG.models[savedModel]) {
            modelSelect.value = savedModel;
        } else {
            modelSelect.value = API_CONFIG.defaultModel;
        }

        // 初始化时更新模型按钮可见性
        updateModelButtonsVisibility();
        });
    }

    // 添加新的滚动函数
    function scrollWhenReady() {
        const isWechat = /MicroMessenger/i.test(navigator.userAgent);
        if (isWechat) return;

        // 使用 requestAnimationFrame 确保在下一帧渲染时执行
        requestAnimationFrame(() => {
            const messagesDiv = document.getElementById('messages');
            const chatContainer = document.getElementById('chat-container');
            
            if (messagesDiv.children.length > 0) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            } else {
                // 如果还没有渲染完成，使用 MutationObserver 继续监听
                const observer = new MutationObserver((mutations, obs) => {
                    if (messagesDiv.children.length > 0) {
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                        obs.disconnect();
                    }
                });
                
                observer.observe(messagesDiv, {
                    childList: true,
                    subtree: true
                });
            }
        });
    }

    async function createNewConversation() {
        // 显示对话类型选择对话框
        const type = await showChatTypeDialog();
        if (!type) return; // 用户取消了选择

        const conversationId = 'conv_' + Date.now();
        // 根据对话类型设置标题
        const typeName = API_CONFIG.chatTypes[type].name;
        const count = Object.values(conversations).filter(conv => conv.type === type).length + 1;
        const title = `${typeName} ${count}`;

        // 创建新会话对象
        conversations[conversationId] = {
            title: title,
            messages: [],
            systemPrompt: API_CONFIG.chatTypes[type].systemPrompt,
            type: type
        };
        saveConversations();
        
        // 在左侧列表的开头添加新会话
        const listElement = document.getElementById('conversation-list');
        const newItem = document.createElement('div');
        newItem.setAttribute('data-id', conversationId);
        newItem.className = `conversation-item p-4 flex justify-between items-center cursor-pointer`;

        newItem.innerHTML = `
            <div class="flex-1 truncate mr-2">${title}</div>
            <button class="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded" title="删除对话">
                <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
        `;

        newItem.querySelector('div').onclick = () => !isGenerating && switchConversation(conversationId);
        newItem.querySelector('button').onclick = (e) => deleteConversation(conversationId, e);

        // 将新会话插入到列表开头
        if (listElement.firstChild) {
            listElement.insertBefore(newItem, listElement.firstChild);
        } else {
            listElement.appendChild(newItem);
        }

        // 设置当前会话ID
        currentConversationId = conversationId;
        localStorage.setItem('lastSelectedConversation', conversationId);
        localStorage.setItem('currentConversationId', conversationId);
        
        // 更新UI状态
        document.getElementById('title-input').classList.add('hidden');
        document.getElementById('current-conversation-title').classList.remove('hidden');
        document.getElementById('current-conversation-title').textContent = title;
        
        // 清空消息区域，而不是获取历史消息
        const messagesDiv = document.getElementById('messages');
        messagesDiv.innerHTML = '';
        messagesDiv.removeAttribute('data-has-more');
        messagesDiv.removeAttribute('data-first-id');
        
        // 更新选中状态
        const previousActive = document.querySelector('.conversation-item.active');
        if (previousActive) {
            previousActive.classList.remove('active');
        }
        newItem.classList.add('active');
        
        updateModelButtonsVisibility();
        localStorage.removeItem('currentTaskId'); 

        // 在手机端自动收起侧边栏
        if (window.innerWidth < 768) {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            sidebar.classList.remove('active');
            overlay?.classList.remove('active');
        }
    }

    function switchConversation(conversationId) {
        // 如果正在生成，不允许切换对话
        if (isGenerating) {
            return;
        }

        // 移除之前的选中状态
        const previousActive = document.querySelector('.conversation-item.active');
        if (previousActive) {
            previousActive.classList.remove('active');
        }
        console.log('switchConversation', conversationId);
        
        currentConversationId = conversationId;
        // // 保存当前选中的对话ID
        // localStorage.setItem('lastSelectedConversation', conversationId);
        // // 保存当前选中的对话ID
        // localStorage.setItem('currentConversationId', conversationId);
        

         // 更新选中状态
        const newActive1 = document.querySelector(`.conversation-item[data-id="${conversationId}"]`);
        if (newActive1) {
            newActive1.classList.add('active');
            // 使用DOM中的标题
            const titleDiv = newActive1.querySelector('.flex-1');
            if (titleDiv) {
                document.getElementById('current-conversation-title').textContent = titleDiv.textContent;
            }
        }

        document.getElementById('title-input').classList.add('hidden');
        document.getElementById('current-conversation-title').classList.remove('hidden');
        //调用获取历史消息的函数
        refreshMessages();       
        // 检查是否在微信浏览器中
        scrollWhenReady();
        
        // 添加新的选中状态
        const newActive = document.querySelector(`.conversation-item[data-id="${conversationId}"]`);
        if (newActive) {
            newActive.classList.add('active');
        }

        // 检查当前选中的模型是否与对话类型匹配
        const currentType = getConversationType(conversations[conversationId]?.messages);
        const activeModel = document.querySelector('.model-btn.active')?.onclick.toString().match(/'([^']+)'/)?.[1];
        
        if (currentType !== 'none' && (!activeModel || API_CONFIG.models[activeModel].type !== 
            (currentType === 'deepseek' ? 'thinking' : 'normal'))) {
            // 自动选择匹配类型的第一个模型
            const matchingModel = Object.entries(API_CONFIG.models).find(([_, info]) => 
                info.type === (currentType === 'deepseek' ? 'thinking' : 'normal'))?.[0];
            if (matchingModel) {
                switchModel(matchingModel);
            }
        }

        // 更新模型按钮的可见性
        updateModelButtonsVisibility();

        // 在手机端自动收起侧边栏
        if (window.innerWidth < 768) {  // 768px 是 md 断点
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebar-overlay');
            sidebar.classList.remove('active');
            overlay?.classList.remove('active');
        }
    }

    async function deleteConversation(conversationId, event) {
        event.stopPropagation();
        if (!(await showDialog('删除确认', '确定要删除这个对话吗？'))) return;

        try {
            // 调用后端删除接口，但不阻止后续操作
            messageHandler.deleteConversation(conversationId, currentUser).catch(error => {
                console.error('后端删除对话失败:', error);
            });
        } catch (error) {
            console.error('删除对话失败:', error);
        }

        // 无论后端是否成功，继续执行本地删除操作
        const conversationItem = document.querySelector(`.conversation-item[data-id="${conversationId}"]`);
        if (conversationItem) {
            conversationItem.remove();
        }

        if (currentConversationId === conversationId) {
            // 获取左侧列表中的第一个对话
            const firstConversation = document.querySelector('.conversation-item');
            if (firstConversation) {
                // 如果还有其他对话，切换到第一个
                const firstConversationId = firstConversation.getAttribute('data-id');
                delete conversations[conversationId];
                saveConversations();
                switchConversation(firstConversationId);
            } else {
                // 如果没有其他对话了，创建新对话
                delete conversations[conversationId];
                saveConversations();
                createNewConversation();
            }
        } else {
            delete conversations[conversationId];
            saveConversations();
        }
    }

    function refreshConversationList(lastId = '') {
        const listElement = document.getElementById('conversation-list');
        
        return new Promise((resolve, reject) => {
                // 如果是首次加载，清空列表并显示加载动画
            if (!lastId) {
                listElement.innerHTML = `
                    <div class="flex justify-center items-center p-4">
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                `;
            }
            // 确保 messageHandler 已初始化
            if (!messageHandler) {
                initMessageHandler();
            }    
            // 获取会话列表
            messageHandler.getConversations({
                user: currentUser,
                lastId: lastId,
                limit: 20
            }).then(response => {
                // 首次加载时清除loading
                if (!lastId) {
                    listElement.innerHTML = '';
                }else {
                    // 加载更多时，先移除原来的加载更多按钮
                    const oldLoadMoreBtn = listElement.querySelector('.load-more-btn');
                    if (oldLoadMoreBtn) {
                        oldLoadMoreBtn.remove();
                    }

                }
                
                has_more = response.has_more;
                last_record_id = response.data.length > 0 ? response.data[response.data.length - 1]?.id : '';

                 // 如果会话列表为空且是首次加载，自动创建一个新会话（不弹窗）
                 if (response.data.length === 0 && !lastId) {
                    console.log('会话列表为空，自动创建新会话');
                    // 直接创建一个普通对话，不调用createNewConversation()
                    const conversationId = 'conv_' + Date.now();
                    const title = '普通对话 1';
                    
                    // 创建新会话对象
                    conversations[conversationId] = {
                        title: title,
                        messages: [],
                        systemPrompt: API_CONFIG.chatTypes.normal.systemPrompt,
                        type: 'normal'
                    };
                    saveConversations();
                    
                    // 在左侧列表添加新会话
                    const newItem = document.createElement('div');
                    newItem.setAttribute('data-id', conversationId);
                    newItem.className = `conversation-item p-4 flex justify-between items-center cursor-pointer`;

                    newItem.innerHTML = `
                        <div class="flex-1 truncate mr-2">${title}</div>
                        <button class="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded" title="删除对话">
                            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                        </button>
                    `;

                    newItem.querySelector('div').onclick = () => !isGenerating && switchConversation(conversationId);
                    newItem.querySelector('button').onclick = (e) => deleteConversation(conversationId, e);
                    
                    listElement.appendChild(newItem);
                    
                    // 设置当前会话ID
                    currentConversationId = conversationId;
                    localStorage.setItem('lastSelectedConversation', conversationId);
                    localStorage.setItem('currentConversationId', conversationId);
                    
                    // 更新UI状态
                    document.getElementById('title-input').classList.add('hidden');
                    document.getElementById('current-conversation-title').classList.remove('hidden');
                    document.getElementById('current-conversation-title').textContent = title;
                    
                    // 清空消息区域
                    const messagesDiv = document.getElementById('messages');
                    messagesDiv.innerHTML = '';
                    messagesDiv.removeAttribute('data-has-more');
                    messagesDiv.removeAttribute('data-first-id');
                    
                    // 更新选中状态
                    newItem.classList.add('active');
                    
                    updateModelButtonsVisibility();
                    
                    resolve(response);
                    return;
                }
                // 渲染会话列表
                response.data.forEach(conv => {
                    const item = document.createElement('div');
                    item.setAttribute('data-id', conv.id);
                    item.className = `conversation-item p-4 flex justify-between items-center ${
                        (isGenerating && conv.id === currentConversationId) ? 'cursor-pointer' : (isGenerating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer')} ${
                        (currentConversationId === conv.id) ? 'active' : ''
                    }`;

                    item.innerHTML = `
                        <div class="flex-1 truncate mr-2">${conv.name}</div>
                        <button class="p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded" title="删除对话">
                            <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    `;

                    item.querySelector('div').onclick = () => !isGenerating && switchConversation(conv.id);
                    item.querySelector('button').onclick = (e) => deleteConversation(conv.id, e);
                    
                    listElement.appendChild(item);
                    //保存到conversations中
                    conversations[conv.id] = {
                        title: conv.name, 
                        type: "normal",
                        systemPrompt: API_CONFIG.chatTypes.normal.systemPrompt,
                        messages: []
                    }
                });
                // saveConversations();
                // 如果是首次加载且有数据，自动选择第一个会话
                if (!lastId && response.data.length > 0) {
                    const firstConversation = response.data[0];
                    switchConversation(firstConversation.id);
                }

                // 添加加载更多按钮
                if (has_more) {
                    const loadMoreBtn = document.createElement('div');
                    loadMoreBtn.className = 'load-more-btn py-1.5 px-2.5 mx-16 my-1 text-center text-[var(--text-primary)] cursor-pointer rounded-md border-2 border-[var(--border-color)] hover:bg-[var(--hover-bg)] transition-colors duration-200 flex items-center justify-center space-x-1 text-xs font-medium';
                    loadMoreBtn.innerHTML = `
                        <svg class="w-2.5 h-2.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        <span>加载更多</span>
                    `;
                    loadMoreBtn.onclick = () => {
                        // 点击时显示 loading 状态
                        loadMoreBtn.innerHTML = `
                            <div class="loading-dots">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        `;
                        loadMoreBtn.classList.add('opacity-50', 'cursor-not-allowed');
                        refreshConversationList(last_record_id);
                    };
                    listElement.appendChild(loadMoreBtn);
                    resolve(response); // Resolve the promise
                }
            }).catch(error => {
                console.error('获取会话列表失败：', error);
                reject(error);
            });
        });
    }

    function saveConversations() {
        localStorage.setItem('conversations', JSON.stringify(conversations));
    }

    function createMessageElement(role, index) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-container rounded-lg ${
            role === 'user'
                ? 'bg-[var(--message-bg-user)] ml-0 md:ml-12 py-3 px-4' // 用户消息使用更紧凑的垂直内边距
                : 'bg-[var(--message-bg-assistant)] mr-0 md:mr-12 p-4'  // 助手消息保持原有内边距
        }`;
        messageDiv.dataset.index = index;

        const containerDiv = document.createElement('div');
        containerDiv.className = 'flex items-start space-x-4';

        const avatar = document.createElement('div');
        avatar.className = `w-8 h-8 rounded-full flex items-center justify-center ${
            role === 'user' ? 'bg-gray-500' : 'bg-blue-600'
        }`;
        
        if (role === 'user') {
            avatar.innerHTML = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class="w-5 h-5"><path d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'/><circle cx='12' cy='7' r='4'/></svg>`;
        } else {
            avatar.innerHTML = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class="w-5 h-5"><path d='M12 2C6.5 2 2 6.5 2 12s4.5 10 10 11c.9 0 1.8-.1 2.6-.4'/><path d='M17.6 14.2c-.8.8-1.3 2-1.1 3.2.2 1.2 1.1 2.2 2.3 2.4 1.2.2 2.4-.3 3.2-1.1.8-.8 1.3-2 1.1-3.2-.2-1.2-1.1-2.2-2.3-2.4-1.2-.2-2.4.3-3.2 1.1z'/><path d='M9.4 9.8c.8-.8 1.3-2 1.1-3.2-.2-1.2-1.1-2.2-2.3-2.4-1.2-.2-2.4.3-3.2 1.1-.8.8-1.3 2-1.1 3.2.2 1.2 1.1 2.2 2.3 2.4 1.2.2 2.4-.3 3.2-1.1z'/><path d='M14.5 8.5l-5 7'/></svg>`;
        }

        const contentContainer = document.createElement('div');
        contentContainer.className = 'flex-1 min-w-0';

        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'message-actions flex space-x-2';

        // 添加复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'p-1 text-[var(--text-secondary)] hover:bg-[var(--hover-bg)] rounded';
        copyButton.innerHTML = `
            <svg class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
        `;
        copyButton.title = '复制消息';
        copyButton.onclick = async (e) => {
            e.stopPropagation();
            const responseContent = textDiv.querySelector('.response-content');
            const messageContent = responseContent ? responseContent.textContent : textDiv.textContent;
            const success = await copyTextToClipboard(messageContent);
            if (success) {
                // 显示复制成功的 toast
                const toast = document.createElement('div');
                toast.className = 'copy-toast';
                toast.innerHTML = `
                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>已复制</span>
                `;
                document.body.appendChild(toast);
                
                // 2秒后移除 toast
                setTimeout(() => {
                    toast.remove();
                }, 2000);
                
                // 按钮视觉反馈
                copyButton.classList.add('copied');
                setTimeout(() => {
                    copyButton.classList.remove('copied');
                }, 1000);
            } else {
                // 显示复制失败的 toast
                const toast = document.createElement('div');
                toast.className = 'copy-toast';
                toast.innerHTML = `
                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span>复制失败</span>
                `;
                document.body.appendChild(toast);
                
                // 2秒后移除 toast
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            }
        };
        actionsDiv.appendChild(copyButton);


        const textDiv = document.createElement('div');
        textDiv.className = 'markdown-body text-[var(--text-primary)]';

        contentContainer.appendChild(actionsDiv);
        contentContainer.appendChild(textDiv);

        containerDiv.appendChild(avatar);
        containerDiv.appendChild(contentContainer);
        messageDiv.appendChild(containerDiv);

        document.getElementById('messages').appendChild(messageDiv);
        return textDiv;
    }


    function refreshMessages(first_id = '') {
    const messagesDiv = document.getElementById('messages');
    const isLoadingMore = first_id !== '';
    
    // 仅在首次加载时清空消息
    if (!isLoadingMore) {
        messagesDiv.innerHTML = '';
    } else {
        // 加载更多时，先移除原来的加载提示
        const oldLoadingIndicator = messagesDiv.querySelector('.load-more-indicator');
        if (oldLoadingIndicator) {
            oldLoadingIndicator.remove();
        }
    }
    
    try {
        if (!messageHandler) {
            initMessageHandler();
        }    
        
        // 如果是加载更多，添加一个加载指示器
        if (isLoadingMore) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'load-more-indicator flex justify-center items-center p-4';
            loadingIndicator.innerHTML = `
                <div class="loading-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            `;
            messagesDiv.insertBefore(loadingIndicator, messagesDiv.firstChild);
        }
        
        // 返回Promise以便可以使用finally方法
        return messageHandler.getMessages({
            user: currentUser,
            conversationId: currentConversationId,
            first_id: first_id, 
            limit: 10
        }).then(messages => {
            // 记录当前滚动位置和高度
            const chatContainer = document.getElementById('chat-container');
            const oldScrollHeight = chatContainer.scrollHeight;
            const oldScrollTop = chatContainer.scrollTop;
            
            // 移除加载指示器
            const loadingIndicator = messagesDiv.querySelector('.load-more-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
            
            // 保存是否有更多消息的状态
            if (messages.has_more && messages.data.length > 0) {
                // 保存第一条消息的ID，用于下次加载更多
                messagesDiv.setAttribute('data-first-id', messages.data[0].id);
                messagesDiv.setAttribute('data-has-more', 'true');
            } else {
                messagesDiv.setAttribute('data-has-more', 'false');
            }
            
            // 创建文档片段提高性能
            const fragment = document.createDocumentFragment();
            
            // 遍历消息并显示到页面
            messages.data.forEach(message => {
                // 检查消息内容是否为空
                if (!message.query && !message.answer) {
                    console.warn('跳过空消息:', message);
                    return; // 跳过空消息
                }
                
                // 创建用户问题元素
                const userDiv = createMessageElement('user', messages.data.length);
                userDiv.style.marginLeft = "-2.2rem";
                userDiv.style.marginTop = "1rem";
                userDiv.innerHTML = marked.parse(message.query);
                
                // 创建AI回答元素
                const assistantDiv = createMessageElement('assistant', messages.data.length);
                assistantDiv.style.marginLeft = "-2.2rem";
                assistantDiv.style.marginTop = "1rem";

                // 处理AI回答中的思考过程
                if (message.answer.includes('<think>')) {
                    // 添加思考过程的HTML结构
                    assistantDiv.innerHTML = thinkingHtml;
                    
                    // 提取思考内容和最终答案
                    const thinkMatch = message.answer.match(/<think>([\s\S]*?)<\/think>/);
                    const thinkContent = thinkMatch ? thinkMatch[1].trim() : '';
                    const finalContent = message.answer.split('</think>')[1]?.trim() || '';
                    
                    // 渲染思考内容
                    const reasoningDiv = assistantDiv.querySelector('.think-content .markdown-body');
                    if (reasoningDiv && thinkContent) {
                        reasoningDiv.innerHTML = marked.parse(thinkContent);
                    }
                    
                    // 渲染最终答案
                    const contentDiv = assistantDiv.querySelector('.response-content');
                    if (contentDiv && finalContent) {
                        contentDiv.innerHTML = marked.parse(finalContent);
                    }
                } else {
                    // 如果没有思考过程，直接显示答案
                    assistantDiv.innerHTML = marked.parse(message.answer);
                }

                // 处理代码高亮
                assistantDiv.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                    addCopyButton(block);
                });
            });

            // 根据加载类型插入元素
            if (isLoadingMore) {
                messagesDiv.insertBefore(fragment, messagesDiv.firstChild);
                
                // 保持滚动位置，避免加载更多内容后页面跳动
                requestAnimationFrame(() => {
                    const newScrollHeight = chatContainer.scrollHeight;
                    chatContainer.scrollTop = oldScrollTop + (newScrollHeight - oldScrollHeight);
                });
            } else {
                messagesDiv.appendChild(fragment);
                // 首次加载滚动到底部
                scrollToBottom();
            }
            
            return messages; // 返回消息数据以便链式调用
        }).catch(error => {
            console.error('获取消息失败:', error);
            // 移除加载指示器
            const loadingIndicator = messagesDiv.querySelector('.load-more-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
            throw error; // 重新抛出错误以便在调用链中处理
        });
    } catch (error) {
        console.error('获取消息失败:', error);
        return Promise.reject(error); // 返回被拒绝的Promise
    }
}

    function addCopyButton(block) {
        // 添加复制按钮到代码块
        const pre = block.parentElement;
        const copyButton = document.createElement('button');
        copyButton.className = 'code-copy-btn';
        copyButton.innerHTML = `
            <svg class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
        `;
        copyButton.title = '复制代码';
        copyButton.onclick = async (e) => {
            e.stopPropagation();
            const success = await copyTextToClipboard(block.textContent);
            if (success) {
                // 显示复制成功的 toast
                const toast = document.createElement('div');
                toast.className = 'copy-toast';
                toast.innerHTML = `
                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>代码已复制</span>
                `;
                document.body.appendChild(toast);
                
                // 2秒后移除 toast
                setTimeout(() => {
                    toast.remove();
                }, 2000);
                
                // 按钮视觉反馈
                copyButton.classList.add('copied');
                setTimeout(() => {
                    copyButton.classList.remove('copied');
                }, 1000);
            } else {
                // 显示复制失败的 toast
                const toast = document.createElement('div');
                toast.className = 'copy-toast';
                toast.innerHTML = `
                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span>复制失败</span>
                `;
                document.body.appendChild(toast);
                
                // 2秒后移除 toast
                setTimeout(() => {
                    toast.remove();
                }, 2000);
            }
        };
        pre.appendChild(copyButton);
    }

    

    // 提取生成消息的公共方法
    async function generateAssistantMessage(messages, assistantDiv, user, previousMessages = null,files = []) {
        let finalContent = '';
        let isThinking = false;
        let thinkingContent = '';
        let firstTokenReceived = false;
        let sendConversationId = currentConversationId || ''
        // 添加加载动画
        assistantDiv.style.marginLeft = "-2.2rem";
        assistantDiv.style.marginTop = "1rem";
        assistantDiv.innerHTML = `
        <div class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        </div>`;

    try {
        // 使用全局的 messageHandler
        if (!messageHandler) {
            initMessageHandler();
        }
        console.log(sendConversationId)  
        // 检查并处理 conv_ 开头的会话ID
        if (sendConversationId.startsWith('conv_')) {
            sendConversationId = '';
        }
        // 先发送消息获取响应
        // 处理文件信息，确保格式正确才发送
        let fileParams = [];
        if (files && files.length > 0) {
            fileParams = files.map(file => ({
                type: file.type || 'document',
                transfer_method: 'local_file',
                upload_file_id: file.upload_file_id
            }));
        }
        const response = await messageHandler.sendMessage(
            messages[messages.length - 1].content,
            {
                conversationId: sendConversationId,
                user: user,
                response_mode: 'streaming',
                files: fileParams
            }
        );

        // 然后处理流式响应
        await messageHandler.handleStreamResponse(response, {
            onConversationId: (id) => {
                 if (currentConversationId !== id) {
                    let oldId = currentConversationId
                      // 保存到 localStorage
                    localStorage.setItem('currentConversationId', id);
                    currentConversationId = id;
                    // 更新左侧列表中的会话项 data-id
                    const conversationItem = document.querySelector(`.conversation-item[data-id="${oldId}"]`);
                    if (conversationItem) {
                        // 更新 data-id
                        conversationItem.setAttribute('data-id', id);
                        
                        // 重新创建并绑定点击事件处理程序
                        const titleDiv = conversationItem.querySelector('div');
                        const deleteBtn = conversationItem.querySelector('button');
                        
                        // 移除旧的事件监听器
                        titleDiv.replaceWith(titleDiv.cloneNode(true));
                        deleteBtn.replaceWith(deleteBtn.cloneNode(true));
                        
                        // 获取新的元素引用并绑定新的事件处理程序
                        const newTitleDiv = conversationItem.querySelector('div');
                        const newDeleteBtn = conversationItem.querySelector('button');
                        
                        newTitleDiv.onclick = () => !isGenerating && switchConversation(id);
                        newDeleteBtn.onclick = (e) => deleteConversation(id, e);
                    }
                    // const oldId = Object.keys(conversations)[0];
                    console.log("新的会话id"+currentConversationId);
                    conversations[id] = conversations[oldId];
                    // 手动设置标题
                   messageHandler.renameConversation(currentConversationId, {
                    name:  conversations[id].title,
                    auto_generate: false,
                    user: user
                   });
                    delete conversations[oldId];
                    saveConversations();
                 }
            },
            onTaskId: (id) => {              
                      // 保存到 localStorage
                    localStorage.setItem('currentTaskId', id);  
            },
            onThinking: (thinking) => {
                if (isPaused) return;
                finalThinking = thinking;
                // 移除加载动画
                if (!firstTokenReceived) {
                    firstTokenReceived = true;
                    assistantDiv.innerHTML = '';
                }
                if (!isThinking) {
                    isThinking = true;
                    thinkingContent = '';
                    assistantDiv.innerHTML = thinkingHtml;
                }
                thinkingContent = thinking;
                const reasoningDiv = assistantDiv.querySelector('.think-content .markdown-body');
                if (reasoningDiv) {
                    reasoningDiv.innerHTML = marked.parse(thinkingContent);
                    reasoningDiv.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                        addCopyButton(block);
                    });
                    if (autoScroll) {
                        scrollToBottom();
                    }
                }
            },

            onAnswer: (answer) => {
                if (isPaused) return;
                finalContent = answer;
                const responseContent = assistantDiv.querySelector('.response-content');
                if (responseContent) {
                    responseContent.innerHTML = marked.parse(finalContent);
                    responseContent.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                        addCopyButton(block);
                    });
                    if (autoScroll) {
                        scrollToBottom();
                    }
                }
            }
        });

        
       // 返回最终的消息内容
       return {
            role: 'assistant',
            type: thinkingContent ? 'thinking' : 'normal',
            content: thinkingContent ? JSON.stringify({
                thinking: thinkingContent,
                content: finalContent
            }) : finalContent
        };

    } catch (error) {
        console.error('Error:', error);
        assistantDiv.innerHTML = `<div class="error-message">生成回答时发生错误</div>`;
        throw error;
    }
    
}

   

    // sendMessage 函数
    async function sendMessage(autoSend = false) {
        // 等待登录验证完成 
        //todo上线变回来
        const isValid = await verifyLogin();
        if (!isValid) {
            console.log('登录验证失败，请重新登录');
            return;  // 登录验证失败，直接返回
        }
        //用户信息
        currentUser = isValid;
        const input = document.getElementById('user-input');
        const message = input.value.trim();
        console.log('发送的消息111：', message);
        // 如果既没有消息也没有待发送的文件，则返回
        if ((!message && (!pendingFiles || pendingFiles.length === 0)) || isGenerating) return;
        
        if (!autoSend) {
            input.value = '';
        }
        
        // 移除文件预览
        const filePreviews = document.querySelectorAll('.file-preview');
        filePreviews.forEach(preview => preview.remove());

        // 构建完整的用户消息
        let fullMessage = message;
        // if (pendingFiles && pendingFiles.length > 0) {
        //     if (pendingFiles.length === 1) {
        //         fullMessage = `我上传了一个文件：${pendingFiles[0].fileName}\n\n\`\`\`\n${pendingFiles[0].content}\n\`\`\``;
        //     } else {
        //         fullMessage = `我上传了 ${pendingFiles.length} 个文件：\n\n`;
        //         pendingFiles.forEach((file, index) => {
        //             fullMessage += `### 文件 ${index + 1}：${file.fileName}\n\n\`\`\`\n${file.content}\n\`\`\`\n\n`;
        //         });
        //     }
            
        //     if (message) {
        //         fullMessage += '\n\n' + message;
        //     }
            
        //     // 清除待发送的文件
        //     pendingFiles = [];
        // } else {
        //     fullMessage = message;
        // }
        
        // 创建用户消息元素
        const userDiv = createMessageElement('user', conversations[currentConversationId].messages.length);
        userDiv.style.marginLeft = "-2.2rem";
        userDiv.style.marginTop = "1rem";
        userDiv.innerHTML = marked.parse(fullMessage);
        
        let searchResults = '';
        
        if (currentRequestController) {
            currentRequestController.abort();
        }
        currentRequestController = new AbortController();
        toggleSendStopButton(true);
        
        autoScroll = true;
        scrollToBottom(true);

        const previousMessages = [...conversations[currentConversationId].messages];

        conversations[currentConversationId].messages.push({
            role: 'user',
            content: fullMessage,
            searchResults: searchResults,
            metadata: pendingFiles && pendingFiles.length > 0 ? {
                type: 'files',
                files: pendingFiles.map(file => ({
                    fileName: file.fileName,
                    type: file.type,
                    fileSize: file.size,
                    fileId:file.upload_file_id
                }))
            } : undefined
        });

        const assistantDiv = createMessageElement('assistant', conversations[currentConversationId].messages.length);
        
        isGenerating = true;

        const newMessage = await generateAssistantMessage(getMessagesWithContext(), assistantDiv, isValid, previousMessages,pendingFiles);
        
        if (newMessage) {
            conversations[currentConversationId].messages.push(newMessage);      
            // 如果这是第一轮对话且标题还是默认的，则自动生user成标题
            if (conversations[currentConversationId].messages.length === 2) {
                // try {
                //     const titleResponse = await fetch(API_CONFIG.defaultUrl, {
                //         method: 'POST',
                //         headers: {
                //             'Content-Type': 'application/json',
                //             'Authorization': `Bearer ${API_CONFIG.defaultKey}`
                //         },
                //         body: JSON.stringify({
                //             model: API_CONFIG.defaultModel,
                //             messages: [
                //                 {
                //                     role: 'system',
                //                     content: '你是一个对话标题生成器。请根据用户的消息和AI的回复生成一个简短的标题（不超过15个字），直接返回标题文本，不要任何多余的话。标题要简洁且能反映对话的主要内容。'
                //                 },
                //                 {
                //                     role: 'user',
                //                     content: `用户问题：${fullMessage}\nAI回复：${newMessage.content}`
                //                 }
                //             ]
                //         })
                //     });

                //     const titleData = await titleResponse.json();
                //     if (titleData.choices && titleData.choices[0] && titleData.choices[0].message) {
                //         const newTitle = titleData.choices[0].message.content.trim();
                //         conversations[currentConversationId].title = newTitle;
                //         document.getElementById('current-conversation-title').textContent = newTitle;
                //         refreshConversationList();
                //     }
                // } catch (error) {
                //     console.error('获取标题失败:', error);
                // }
            }
            
            saveConversations(); 
        }
        
        currentRequestController = null;
        toggleSendStopButton(false);
        isGenerating = false;
        pendingFiles = [];
        // refreshMessages(); 
    }

    function getMessagesWithContext() {
        const allMessages = conversations[currentConversationId].messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
        const currentIndex = allMessages.length - 1;
        
        // 获取当前对话的系统提示词
        let systemPrompt = conversations[currentConversationId].systemPrompt || API_CONFIG.defaultSystemPrompt;
        
        // 如果有搜索结果，将其添加到系统提示词中，并添加引用说明
        if (allMessages.length > 0 && conversations[currentConversationId].messages[currentIndex].searchResults) {
            systemPrompt += '\n\n以下是与用户问题相关的搜索结果，你可以参考这些信息来回答。在引用信息时，请使用markdown格式的链接，例如：根据[来源1](链接)显示...。这样可以让用户直接点击查看原始来源：\n' + 
                conversations[currentConversationId].messages[currentIndex].searchResults;
        }
        
        // 如果消息数量小于等于上下文限制，返回所有消息
        if (allMessages.length <= API_CONFIG.contextCount) {
            return [
                { role: 'system', content: systemPrompt },
                ...allMessages
            ];
        }
        
        // 获取最近的几条消息作为上下文
        const contextMessages = allMessages.slice(-API_CONFIG.contextCount - 1);
        
        return [
            {
                role: 'system',
                content: `${systemPrompt}\n\n这是一段对话的继续，之前已经进行了 ${currentIndex - API_CONFIG.contextCount} 轮对话。`
            },
            ...contextMessages
        ];
    }

    


    function scrollToBottom(force = false) {
        const chatContainer = document.getElementById('chat-container');
        const lastMessage = chatContainer.querySelector('#messages > div:last-child');
        const scrollButton = document.getElementById('scroll-to-bottom-btn');

        if (lastMessage) {
            requestAnimationFrame(() => {
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // 更新按钮可见性
                const isNearBottom = chatContainer.scrollHeight - chatContainer.scrollTop - chatContainer.clientHeight < 100;
                scrollButton.classList.toggle('visible', !isNearBottom);
                
                // 当滚动到底部时，重新启用自动滚动
                if (isNearBottom) {
                    autoScroll = true;
                }
            });
        }
    }

    function toggleSendStopButton(isGenerating) {
        const sendButton = document.getElementById('send-button');
        const stopButton = document.getElementById('stop-button');
        const pauseButton = document.getElementById('pause-button');
        const newChatButton = document.getElementById('new-chat-btn');  // 添加这行

        if (isGenerating) {
            sendButton.disabled = true;
            sendButton.classList.add('opacity-50', 'cursor-not-allowed');
            stopButton.classList.remove('hidden');
            pauseButton.classList.remove('hidden');
            const pauseIcon = document.getElementById('pause-icon');
            const playIcon = document.getElementById('play-icon');
            pauseIcon.classList.remove('hidden');
            playIcon.classList.add('hidden');
            pauseButton.classList.add('text-[var(--text-secondary)]');
            
            // 禁用新建对话按钮
            newChatButton.disabled = true;
            newChatButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            sendButton.disabled = false;
            sendButton.classList.remove('opacity-50', 'cursor-not-allowed');
            stopButton.classList.add('hidden');
            pauseButton.classList.add('hidden');
            isPaused = false;
            
            // 启用新建对话按钮
            newChatButton.disabled = false;
            newChatButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    function stopGeneration() {
        if (currentRequestController) {
            currentRequestController.abort();
            currentRequestController = null;
            isPaused = false;
            toggleSendStopButton(false);
            isGenerating = false;
            // refreshConversationList();  // 停止生成时更新列表
        }
    }

    function startEditingTitle(titleElement) {
        const input = document.getElementById('title-input');
        input.value = titleElement.textContent;
        titleElement.classList.add('hidden');
        input.classList.remove('hidden');
        input.focus();
        input.select();
    }

    function saveTitle() {
        console.log("saveTitle");
        const titleElement = document.getElementById('current-conversation-title');
        const input = document.getElementById('title-input');
        const newTitle = input.value.trim();
        
        if (newTitle && currentConversationId) {
            // conversations[currentConversationId].title = newTitle;
            titleElement.textContent = newTitle;
            const conversationItem = document.querySelector(`.conversation-item[data-id="${currentConversationId}"]`);
            if (conversationItem) {
                const titleDiv = conversationItem.querySelector('.flex-1');
                if (titleDiv) {
                    titleDiv.textContent = newTitle;
                }
            }
            //调用会话重命名接口
            // 使用全局的 messageHandler
            if (!messageHandler) {
                initMessageHandler();
            }
            messageHandler.renameConversation(
                localStorage.getItem('currentConversationId')    
            , {
                name: newTitle,
                auto_generate: false,
                user: currentUser   
            })
            saveConversations();
            // refreshConversationList();
        }
        
        titleElement.classList.remove('hidden');
        input.classList.add('hidden');
    }

    function handleTitleKeyPress(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            saveTitle();
        }
    }

    async function togglePause() {
        const pauseButton = document.getElementById('pause-button');
        const pauseIcon = document.getElementById('pause-icon');
        const playIcon = document.getElementById('play-icon');

        isPaused = !isPaused;
        
        if (isPaused) {
            pauseIcon.classList.add('hidden');
            playIcon.classList.remove('hidden');
        } else {
            pauseIcon.classList.remove('hidden');
            playIcon.classList.add('hidden');
        }
    }

    function switchModel(modelId) {
        const modelSelect = document.getElementById('model-select');
        
        // 检查当前对话类型
        const currentType = getConversationType(conversations[currentConversationId]?.messages);
        
        // 如果已有对话，检查模型兼容性
        if (currentType !== 'none') {
            const modelType = API_CONFIG.models[modelId].type;
            const currentModelType = currentType === 'deepseek' ? 'thinking' : 'normal';
            
            if (modelType !== currentModelType) {
                showAlert('不能在同一个对话中混用深度思考模型和普通模型');
                // 恢复之前的选择
                modelSelect.value = localStorage.getItem('preferred-model') || 'deepseek-r1';
                return;
            }
        }
        
        // 保存选择的模型
        localStorage.setItem('preferred-model', modelId);
        // 更新下拉框的值
        modelSelect.value = modelId;
    }

    async function showDialog(title, message) {
        return new Promise((resolve) => {
            const dialog = document.getElementById('custom-dialog');
            const titleEl = document.getElementById('dialog-title');
            const messageEl = document.getElementById('dialog-message');
            const confirmBtn = document.getElementById('dialog-confirm');
            const cancelBtn = document.getElementById('dialog-cancel');

            titleEl.textContent = title;
            messageEl.textContent = message;
            dialog.classList.remove('hidden');

            const handleConfirm = () => {
                dialog.classList.add('hidden');
                cleanup();
                resolve(true);
            };

            const handleCancel = () => {
                dialog.classList.add('hidden');
                cleanup();
                resolve(false);
            };

            const cleanup = () => {
                confirmBtn.removeEventListener('click', handleConfirm);
                cancelBtn.removeEventListener('click', handleCancel);
            };

            confirmBtn.addEventListener('click', handleConfirm);
            cancelBtn.addEventListener('click', handleCancel);
        });
    }

    function showAlert(message) {
        return new Promise((resolve) => {
            const alert = document.getElementById('custom-alert');
            const messageEl = document.getElementById('alert-message');
            const confirmBtn = document.getElementById('alert-confirm');

            messageEl.textContent = message;
            alert.classList.remove('hidden');

            const handleConfirm = () => {
                alert.classList.add('hidden');
                confirmBtn.removeEventListener('click', handleConfirm);
                resolve();
            };

            confirmBtn.addEventListener('click', handleConfirm);
        });
    }

    // 添加函数来判断对话类型
    function getConversationType(messages) {
        if (!messages || messages.length === 0) return 'none';
        
        for (const msg of messages) {
            if (msg.role === 'assistant') {
                if (msg.type === 'thinking') return 'deepseek';
                if (msg.type === 'normal') return 'normal';
            }
        }
        return 'none';
    }

    // 添加更新模型按钮可见性的函数
    function updateModelButtonsVisibility() {
        const currentType = getConversationType(conversations[currentConversationId]?.messages);
        const modelSelect = document.getElementById('model-select');
        
        // 获取所有选项
        const options = Array.from(modelSelect.options);
        
        if (currentType === 'none') {
            // 新对话，显示所有选项
            options.forEach(opt => {
                opt.style.display = '';
            });
             // 设置默认选项为第一个可用的模型 内外网选择
              modelSelect.value = options[0].value;
        } else {
            // 根据类型显示对应选项
            const targetType = currentType === 'deepseek' ? 'thinking' : 'normal';
            
            options.forEach(opt => {
                const modelInfo = API_CONFIG.models[opt.value];
                opt.style.display = modelInfo.type === targetType ? '' : 'none';
            });

            // 如果当前选中的模型类型不匹配，切换到第一个可用的模型
            const currentModelInfo = API_CONFIG.models[modelSelect.value];
            if (currentModelInfo.type !== targetType) {
                const firstMatchingOption = options.find(opt => 
                    API_CONFIG.models[opt.value].type === targetType && opt.style.display !== 'none'
                );
                if (firstMatchingOption) {
                    modelSelect.value = firstMatchingOption.value;
                }
            }
        }
    }

    function toggleThinking(header) {
        const content = header.nextElementSibling;
        const icon = header.querySelector('.think-header-icon');
        const isCollapsed = content.classList.contains('collapsed');
        
        // 如果要展开
        if (isCollapsed) {
            content.style.display = 'block';
            // 使用 requestAnimationFrame 确保 display 更改已应用
            requestAnimationFrame(() => {
                content.classList.remove('collapsed');
                icon.style.transform = 'rotate(0deg)';
            });
        } else {
            // 如果要收起
            content.classList.add('collapsed');
            icon.style.transform = 'rotate(-90deg)';
            // 等待动画完成后隐藏元素
            content.addEventListener('transitionend', function handler() {
                if (!content.classList.contains('collapsed')) return;
                content.style.display = 'none';
                content.removeEventListener('transitionend', handler);
            });
        }
    }


    function openSystemPromptModal() {
        const modal = document.getElementById('system-prompt-modal');
        const systemPrompt = document.getElementById('system-prompt');
        const currentChat = conversations[currentConversationId];
        
        systemPrompt.value = currentChat?.systemPrompt || API_CONFIG.defaultSystemPrompt;
        modal.classList.remove('hidden');
    }
    
    
    

    // 添加对话类型选择对话框
    function showChatTypeDialog() {
        return new Promise((resolve) => {
            const dialog = document.createElement('div');
            dialog.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            dialog.innerHTML = `
                <div class="bg-[var(--bg-primary)] rounded-lg shadow-xl max-w-sm w-[calc(100%-2rem)] mx-4">
                    <div class="p-4 border-b border-[var(--border-color)]">
                        <h3 class="text-lg font-medium">选择对话类型</h3>
                    </div>
                    <div class="p-4 space-y-4">
                        ${Object.entries(API_CONFIG.chatTypes).map(([key, type]) => `
                            <button class="w-full p-4 text-left rounded-lg border border-[var(--border-color)] hover:bg-[var(--hover-bg)] transition-colors"
                                    onclick="selectChatType('${key}')">
                                <div class="font-medium">${type.name}</div>
                            </button>
                        `).join('')}
                    </div>
                </div>
            `;
            document.body.appendChild(dialog);
            
            // 添加点击背景关闭的功能
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    document.body.removeChild(dialog);
                    resolve(null);
                }
            });
            
            // 添加选择类型的函数到 window
            window.selectChatType = (type) => {
                document.body.removeChild(dialog);
                resolve(type);
            };
        });
    }

    // 格式化搜索结果
    function formatSearchResults(results) {
        if (!results.length) return '';
        
        // Sort results by dateLastCrawled in descending order
        results.sort((a, b) => new Date(b.dateLastCrawled) - new Date(a.dateLastCrawled));
        
        // 使用 markdown 格式的链接
        return results.map((result, index) => {
            const sourceNumber = index + 1;
            return `[来源${sourceNumber}] ${result.name}
链接：[${result.url}](${result.url})
摘要：${result.summary}
---`;
        }).join('\n\n');
    }


    async function handleFileImport(input) {
        const file = input.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const chatData = JSON.parse(text);

            // 验证导入的数据格式
            if (!chatData.title || !Array.isArray(chatData.messages)) {
                throw new Error('无效的对话文件格式');
            }

            // 创建新的对话 ID
            const conversationId = 'conv_' + Date.now();
            
            // 构建新的对话对象
            conversations[conversationId] = {
                title: chatData.title,
                messages: chatData.messages,
                systemPrompt: chatData.systemPrompt || API_CONFIG.defaultSystemPrompt,
                type: chatData.type || 'normal'
            };

            // 保存并切换到新导入的对话
            saveConversations();
            switchConversation(conversationId);
            refreshConversationList();
            
            // 显示成功提示
            showAlert('对话导入成功');
            
            // 关闭设置面板
            closeSystemPromptModal();
        } catch (error) {
            showAlert('导入失败：' + (error.message || '文件格式错误'));
        }

        // 清除文件输入，允许重复导入相同文件
        input.value = '';
    }


// 修改文件处理函数
async function handleFileUpload(input) {
    const files = input.files;
    if (!files || files.length === 0) return;
    
    // 检查是否超过最大文件数量限制
    if (pendingFiles.length + files.length > 3) {
        showAlert('最多只能上传3个文件');
        input.value = '';
        return;
    }

       // 检查单个文件大小限制（50MB = 50 * 1024 * 1024 字节）
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    for (let i = 0; i < files.length; i++) {
        if (files[i].size > MAX_FILE_SIZE) {
            showAlert(`文件 "${files[i].name}" 超过最大限制（50MB），请选择更小的文件`);
            input.value = '';
            return;
        }
    }
    
    
    // 显示加载中提示
    const loadingToast = document.createElement('div');
    loadingToast.className = 'copy-toast';
    loadingToast.innerHTML = `
        <svg class="w-5 h-5 text-blue-500 animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>正在上传文件...</span>
    `;
    document.body.appendChild(loadingToast);

    try {
        // 处理每个文件
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // 获取文件扩展名并转换为大写
            const extension = file.name.split('.').pop().toUpperCase();
            
            // 确定文件类型
            let fileType = FILE_TYPE_MAPPING[extension] || 'custom';
            
            // 上传文件
            const result = await messageHandler.uploadFile(file, {
                user: currentUser
            });
            
            // 创建文件预览元素
            const previewDiv = document.createElement('div');
            previewDiv.className = 'file-preview mb-2 p-3 bg-[var(--bg-secondary)] rounded-lg flex flex-col gap-2';
            
            // 根据文件类型选择图标
            let fileIcon = '';
            if (fileType === 'document') {
                fileIcon = `
                    <svg class="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M10 10.5h4m-4 3h4m-4 3h4" />
                    </svg>
                `;
            } else if (fileType === 'image') {
                fileIcon = `
                    <svg class="w-8 h-8 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                `;
            } else if (fileType === 'audio') {
                fileIcon = `
                    <svg class="w-8 h-8 text-purple-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                    </svg>
                `;
            } else if (fileType === 'video') {
                fileIcon = `
                    <svg class="w-8 h-8 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                `;
            } else {
                fileIcon = `
                    <svg class="w-8 h-8 text-[var(--text-secondary)]" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                `;
            }
            
            previewDiv.innerHTML = `
                <div class="flex items-center gap-3">
                    <div class="flex-shrink-0">
                        ${fileIcon}
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-[var(--text-primary)] truncate">${file.name}</p>
                        <p class="text-xs text-[var(--text-secondary)]">${extension} ${(file.size / 1024).toFixed(1)}KB</p>
                    </div>
                    <button class="file-close-btn p-1 text-[var(--text-secondary)] hover:text-[var(--button-primary-bg)] rounded-full hover:bg-[var(--hover-bg)] transition-colors" title="取消上传">
                        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;

            // 为每个文件预览添加唯一ID
            const fileId = result.id;
            previewDiv.dataset.fileId = fileId;

            // 添加关闭按钮的点击事件
            const closeBtn = previewDiv.querySelector('.file-close-btn');
            closeBtn.addEventListener('click', function() {
                // 移除文件预览
                previewDiv.remove();
                // 从待发送文件列表中移除
                pendingFiles = pendingFiles.filter(f => f.upload_file_id !== fileId);
            });

            // 将预览添加到消息区域
            const messagesDiv = document.getElementById('messages');
            messagesDiv.appendChild(previewDiv);

            // 保存文件信息到待发送列表
            pendingFiles.push({
                type: fileType,
                transfer_method: 'local_file',
                upload_file_id: fileId,
                file_name: file.name,
                file_size: file.size,
            });
            console.log('待发送文件列表：', pendingFiles);
        }

        // 自动滚动到底部
        scrollToBottom(true);
        
        // 移除加载提示
        loadingToast.remove();
        
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'copy-toast';
        toast.innerHTML = `
            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>${files.length > 1 ? `${files.length} 个文件已上传` : '文件已上传'}</span>
        `;
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(() => {
            toast.remove();
        }, 2000);
    } catch (error) {
        // 移除加载提示
        loadingToast.remove();
        
        showAlert('上传文件失败：' + (error.message || '未知错误'));
    }

    // 清除文件输入，允许重复上传相同文件
    input.value = '';
}
    
    

    // 获取URL参数中的token值
    function getTokenFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('token');
    }

        // 添加登录验证函数
        async function verifyLogin() {
             //判断token是否存在然后进行调取token接口
            const token = getTokenFromUrl();
            if (!token) {
                //todo 弹窗提示
                showAlert('登录状态过期，请重新登录');
                return false;
            }
            try {
                const result = await deepseekLogin(token);
                const isInvalid = result.indexOf('code') !== -1;             
                if (isInvalid) {
                    showAlert('登录状态过期，请重新登录');
                    return false;
                }              
                console.log('登录成功，正确的 token');
                return result;
            } catch (error) {
                console.log('登录失败:', error);
                showAlert('登录失败，请检查网络连接');
                return false;
            }
        }

      // 在页面加载时初始化搜索设置
      document.addEventListener('DOMContentLoaded',  () => {
        initScrollListener();
        // 从localStorage加载搜索设置
        const searchEnabled = localStorage.getItem('search_enabled');
        if (searchEnabled !== null) {
            API_CONFIG.search.enabled = searchEnabled === 'true';
        }
        //todo上线变回来
        verifyLogin().then(isValid => {
            if (!isValid) {
                return ;
            }
            currentUser = isValid
            console.log('当前用户：', currentUser);
             
            // 添加 Ctrl+Enter 快捷键支持
            document.getElementById('user-input').addEventListener('keydown', function(e) {
                // 检查是否按下了 Ctrl+Enter
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault(); // 阻止默认行为
                    sendMessage(); // 调用发送消息函数
                }
            });
            initialize(); // 初始化页面
        }); 
    });


// 添加滚动监听，检测到顶部时加载更多消息
function initScrollListener() {
    const chatContainer = document.getElementById('chat-container');
    let isLoading = false;
    
    chatContainer.addEventListener('scroll', () => {
        const messagesDiv = document.getElementById('messages');
        const hasMore = messagesDiv.getAttribute('data-has-more') === 'true';
        const firstId = messagesDiv.getAttribute('data-first-id');
        
        // 当滚动到顶部且有更多消息可加载且当前没有正在加载时
        if (chatContainer.scrollTop < 50 && hasMore && firstId && !isLoading) {
            isLoading = true;
            
            // 加载更多消息
            refreshMessages(firstId).finally(() => {
                isLoading = false;
            });
        }
    });
}
    
</script>
</body>
</html>
